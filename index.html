<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Kripto Balina Takip Sistemi</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="Gerçek zamanlı kripto para balina işlemlerini takip edin">
    <meta name="theme-color" content="#00d4ff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Balina Takip">
    <meta name="msapplication-TileColor" content="#00d4ff">

    <!-- <PERSON><PERSON> Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Stylesheets -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <div class="turkish-flag">
                    <div class="flag-background">
                        <div class="crescent"></div>
                        <div class="star"></div>
                    </div>
                </div>
                <i class="fas fa-whale"></i>
                <h1 class="turkish-flag-text">
                    <span class="flag-text-red">Balina</span>
                    <span class="flag-text-white">Takip</span>
                </h1>
                <span class="live-indicator">
                    <i class="fas fa-broadcast-tower"></i>
                    <span>Gerçek Zamanlı</span>
                </span>
            </div>
            <nav class="nav">
                <a href="#dashboard" class="nav-link active">Dashboard</a>
                <a href="#portfolio" class="nav-link">Portföy</a>
                <a href="#analytics" class="nav-link">Analitik</a>
                <a href="#reports" class="nav-link">Raporlar</a>
                <!-- Tema değiştirme kaldırıldı -->
                <button class="fullscreen-toggle" onclick="toggleFullscreen()" title="Tam Ekran">
                    <i class="fas fa-expand" id="fullscreenIcon"></i>
                </button>
            </nav>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <!-- İstatistik Kartları -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-whale"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Toplam Balina İşlemi</h3>
                            <p class="stat-number" id="totalWhales">0</p>
                            <span class="stat-change positive">+12%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Toplam Hacim</h3>
                            <p class="stat-number" id="totalVolume">$0</p>
                            <span class="stat-change positive">+8.5%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3>En Büyük İşlem</h3>
                            <p class="stat-number" id="largestTx">$0</p>
                            <span class="stat-change negative">-2.1%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Son 24 Saat</h3>
                            <p class="stat-number" id="last24h">0</p>
                            <span class="stat-change positive">+15.3%</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Filtreler -->
            <section class="filters-section">
                <div class="filters">
                    <div class="filter-group">
                        <label for="coinFilter">Kripto Para:</label>
                        <select id="coinFilter">
                            <option value="all">Tümü</option>
                            <optgroup label="🏆 Top 10 Kripto Paralar">
                                <option value="BTC">Bitcoin (BTC)</option>
                                <option value="ETH">Ethereum (ETH)</option>
                                <option value="USDT">Tether (USDT)</option>
                                <option value="BNB">BNB (BNB)</option>
                                <option value="SOL">Solana (SOL)</option>
                                <option value="USDC">USD Coin (USDC)</option>
                                <option value="XRP">XRP (XRP)</option>
                                <option value="DOGE">Dogecoin (DOGE)</option>
                                <option value="ADA">Cardano (ADA)</option>
                                <option value="AVAX">Avalanche (AVAX)</option>
                            </optgroup>
                            <optgroup label="🔥 Layer 1 Blockchainler">
                                <option value="DOT">Polkadot (DOT)</option>
                                <option value="ATOM">Cosmos (ATOM)</option>
                                <option value="NEAR">NEAR Protocol (NEAR)</option>
                                <option value="ALGO">Algorand (ALGO)</option>
                                <option value="FTM">Fantom (FTM)</option>
                                <option value="ONE">Harmony (ONE)</option>
                                <option value="LUNA">Terra Luna (LUNA)</option>
                                <option value="FLOW">Flow (FLOW)</option>
                                <option value="EGLD">MultiversX (EGLD)</option>
                                <option value="ROSE">Oasis Network (ROSE)</option>
                            </optgroup>
                            <optgroup label="🚀 DeFi Protokolleri">
                                <option value="UNI">Uniswap (UNI)</option>
                                <option value="LINK">Chainlink (LINK)</option>
                                <option value="AAVE">Aave (AAVE)</option>
                                <option value="COMP">Compound (COMP)</option>
                                <option value="MKR">Maker (MKR)</option>
                                <option value="SNX">Synthetix (SNX)</option>
                                <option value="CRV">Curve DAO (CRV)</option>
                                <option value="1INCH">1inch (1INCH)</option>
                                <option value="SUSHI">SushiSwap (SUSHI)</option>
                                <option value="YFI">Yearn Finance (YFI)</option>
                            </optgroup>
                            <optgroup label="⚡ Layer 2 & Scaling">
                                <option value="MATIC">Polygon (MATIC)</option>
                                <option value="OP">Optimism (OP)</option>
                                <option value="ARB">Arbitrum (ARB)</option>
                                <option value="LRC">Loopring (LRC)</option>
                                <option value="IMX">Immutable X (IMX)</option>
                                <option value="METIS">Metis (METIS)</option>
                            </optgroup>
                            <optgroup label="🎮 Gaming & Metaverse">
                                <option value="SAND">The Sandbox (SAND)</option>
                                <option value="MANA">Decentraland (MANA)</option>
                                <option value="AXS">Axie Infinity (AXS)</option>
                                <option value="GALA">Gala (GALA)</option>
                                <option value="ENJ">Enjin Coin (ENJ)</option>
                                <option value="ILV">Illuvium (ILV)</option>
                                <option value="ALICE">MyNeighborAlice (ALICE)</option>
                                <option value="TLM">Alien Worlds (TLM)</option>
                                <option value="GHST">Aavegotchi (GHST)</option>
                            </optgroup>
                            <optgroup label="🔮 AI & Big Data">
                                <option value="FET">Fetch.ai (FET)</option>
                                <option value="OCEAN">Ocean Protocol (OCEAN)</option>
                                <option value="GRT">The Graph (GRT)</option>
                                <option value="RNDR">Render Token (RNDR)</option>
                                <option value="AGIX">SingularityNET (AGIX)</option>
                                <option value="NMR">Numeraire (NMR)</option>
                            </optgroup>
                            <optgroup label="💰 Stablecoinler">
                                <option value="USDT">Tether (USDT)</option>
                                <option value="USDC">USD Coin (USDC)</option>
                                <option value="DAI">Dai (DAI)</option>
                                <option value="BUSD">Binance USD (BUSD)</option>
                                <option value="FRAX">Frax (FRAX)</option>
                                <option value="TUSD">TrueUSD (TUSD)</option>
                            </optgroup>
                            <optgroup label="🐕 Meme Coinler">
                                <option value="DOGE">Dogecoin (DOGE)</option>
                                <option value="SHIB">Shiba Inu (SHIB)</option>
                                <option value="PEPE">Pepe (PEPE)</option>
                                <option value="FLOKI">Floki (FLOKI)</option>
                                <option value="BABYDOGE">Baby Doge Coin (BABYDOGE)</option>
                                <option value="ELON">Dogelon Mars (ELON)</option>
                            </optgroup>
                            <optgroup label="🏛️ Klasik Altcoinler">
                                <option value="LTC">Litecoin (LTC)</option>
                                <option value="ETC">Ethereum Classic (ETC)</option>
                                <option value="BCH">Bitcoin Cash (BCH)</option>
                                <option value="XMR">Monero (XMR)</option>
                                <option value="ZEC">Zcash (ZEC)</option>
                                <option value="DASH">Dash (DASH)</option>
                                <option value="XLM">Stellar (XLM)</option>
                                <option value="VET">VeChain (VET)</option>
                                <option value="TRX">TRON (TRX)</option>
                                <option value="EOS">EOS (EOS)</option>
                            </optgroup>
                            <optgroup label="🔗 Interoperability">
                                <option value="DOT">Polkadot (DOT)</option>
                                <option value="ATOM">Cosmos (ATOM)</option>
                                <option value="RUNE">THORChain (RUNE)</option>
                                <option value="IBC">Inter Blockchain (IBC)</option>
                            </optgroup>
                            <optgroup label="🌐 Web3 & Storage">
                                <option value="FIL">Filecoin (FIL)</option>
                                <option value="AR">Arweave (AR)</option>
                                <option value="STORJ">Storj (STORJ)</option>
                                <option value="SC">Siacoin (SC)</option>
                            </optgroup>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="minAmount">Min. Tutar ($):</label>
                        <input type="number" id="minAmount" placeholder="1000000" min="100000">
                    </div>
                    <div class="filter-group">
                        <label for="timeFilter">Zaman:</label>
                        <select id="timeFilter">
                            <option value="1h">Son 1 Saat</option>
                            <option value="24h" selected>Son 24 Saat</option>
                            <option value="7d">Son 7 Gün</option>
                            <option value="30d">Son 30 Gün</option>
                        </select>
                    </div>
                    <button class="filter-btn" onclick="applyFilters()">
                        <i class="fas fa-filter"></i> Filtrele
                    </button>
                </div>
            </section>

            <!-- Grafik Bölümü -->
            <section class="chart-section">
                <div class="chart-container">
                    <h2>Balina İşlem Hacmi Trendi</h2>
                    <canvas id="whaleChart"></canvas>
                </div>
            </section>

            <!-- Balina İşlemleri Tablosu -->
            <section class="transactions-section">
                <div class="section-header">
                    <h2>Canlı Balina İşlemleri</h2>
                    <div class="status-indicator">
                        <span class="status-dot"></span>
                        Canlı
                    </div>
                </div>
                <div class="table-container">
                    <table class="whale-table">
                        <thead>
                            <tr>
                                <th>Zaman</th>
                                <th>Kripto Para</th>
                                <th>Miktar</th>
                                <th>USD Değeri</th>
                                <th>İşlem Hash</th>
                                <th>Tip</th>
                                <th>Durum</th>
                            </tr>
                        </thead>
                        <tbody id="whaleTableBody">
                            <!-- JavaScript ile doldurulacak -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Piyasa Analizi Bölümü -->
            <section class="market-analysis-section">
                <div class="analysis-header-section">
                    <h2>🔮 Gelişmiş Balina Analizi & Piyasa Tahmini</h2>
                    <div class="analysis-controls">
                        <div class="coin-selector">
                            <label for="analysisCoinFilter">Analiz Coin:</label>
                            <select id="analysisCoinFilter" onchange="updateCoinAnalysis()">
                                <option value="all">Genel Piyasa</option>
                                <optgroup label="🏆 Top Kripto Paralar">
                                    <option value="BTC">Bitcoin (BTC)</option>
                                    <option value="ETH">Ethereum (ETH)</option>
                                    <option value="BNB">BNB (BNB)</option>
                                    <option value="SOL">Solana (SOL)</option>
                                    <option value="XRP">XRP (XRP)</option>
                                    <option value="DOGE">Dogecoin (DOGE)</option>
                                    <option value="ADA">Cardano (ADA)</option>
                                    <option value="AVAX">Avalanche (AVAX)</option>
                                </optgroup>
                                <optgroup label="🚀 DeFi Favorileri">
                                    <option value="UNI">Uniswap (UNI)</option>
                                    <option value="LINK">Chainlink (LINK)</option>
                                    <option value="AAVE">Aave (AAVE)</option>
                                    <option value="MATIC">Polygon (MATIC)</option>
                                    <option value="MKR">Maker (MKR)</option>
                                    <option value="COMP">Compound (COMP)</option>
                                </optgroup>
                                <optgroup label="🎮 Gaming & NFT">
                                    <option value="SAND">The Sandbox (SAND)</option>
                                    <option value="MANA">Decentraland (MANA)</option>
                                    <option value="AXS">Axie Infinity (AXS)</option>
                                    <option value="GALA">Gala (GALA)</option>
                                    <option value="ENJ">Enjin Coin (ENJ)</option>
                                </optgroup>
                                <optgroup label="🐕 Meme Coinler">
                                    <option value="DOGE">Dogecoin (DOGE)</option>
                                    <option value="SHIB">Shiba Inu (SHIB)</option>
                                    <option value="PEPE">Pepe (PEPE)</option>
                                    <option value="FLOKI">Floki (FLOKI)</option>
                                </optgroup>
                                <optgroup label="🏛️ Klasikler">
                                    <option value="LTC">Litecoin (LTC)</option>
                                    <option value="ETC">Ethereum Classic (ETC)</option>
                                    <option value="BCH">Bitcoin Cash (BCH)</option>
                                    <option value="XMR">Monero (XMR)</option>
                                </optgroup>
                            </select>
                        </div>
                        <div class="timeframe-selector">
                            <label for="analysisTimeframe">Zaman Aralığı:</label>
                            <select id="analysisTimeframe" onchange="updateCoinAnalysis()">
                                <option value="1h" class="short-term">⚠️ Son 1 Saat (Kısa Vadeli)</option>
                                <option value="4h" selected>✅ Son 4 Saat (Önerilen)</option>
                                <option value="12h">Son 12 Saat</option>
                                <option value="24h">Son 24 Saat</option>
                                <option value="3d">Son 3 Gün</option>
                                <option value="7d">Son 7 Gün (Trend Analizi)</option>
                                <option value="30d">Son 30 Gün (Uzun Vadeli)</option>
                            </select>
                        </div>
                        <div class="analysis-mode">
                            <label for="analysisMode">Analiz Modu:</label>
                            <select id="analysisMode" onchange="updateCoinAnalysis()">
                                <option value="quick">⚡ Hızlı Modu</option>
                                <option value="standard">📊 Standart</option>
                                <option value="advanced" selected>🔮 Gelişmiş</option>
                                <option value="professional">💎 Profesyonel</option>
                                <option value="ai">🤖 AI Modu</option>
                                <option value="research">🔬 Araştırma</option>
                                <option value="technical">📈 Teknik</option>
                                <option value="auto">🎯 Otomatik</option>
                            </select>
                        </div>
                        <div class="mode-info">
                            <button class="mode-help-btn" onclick="showModeHelp()" title="Mod Açıklamaları">
                                <i class="fas fa-question-circle"></i>
                            </button>
                            <button class="mode-compare-btn" onclick="showModeComparison()" title="Mod Karşılaştırması">
                                <i class="fas fa-balance-scale"></i>
                            </button>
                            <button class="mode-history-btn" onclick="showModeHistory()" title="Mod Geçmişi">
                                <i class="fas fa-history"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="analysis-grid">
                    <div class="analysis-card overall-sentiment">
                        <div class="analysis-header">
                            <i class="fas fa-chart-line"></i>
                            <h3 id="sentimentTitle">Genel Piyasa Durumu</h3>
                        </div>
                        <div class="sentiment-indicator" id="overallSentiment">
                            <div class="sentiment-value">Analiz Ediliyor...</div>
                            <div class="sentiment-description">Balina hareketleri analiz ediliyor</div>
                        </div>
                    </div>

                    <div class="analysis-card buy-sell-ratio">
                        <div class="analysis-header">
                            <i class="fas fa-balance-scale"></i>
                            <h3 id="ratioTitle">Alış/Satış Oranı</h3>
                        </div>
                        <div class="ratio-display" id="buySellRatio">
                            <div class="ratio-bars">
                                <div class="buy-bar">
                                    <span>Alış</span>
                                    <div class="bar-fill buy-fill" id="buyBar"></div>
                                    <span id="buyPercentage">0%</span>
                                </div>
                                <div class="sell-bar">
                                    <span>Satış</span>
                                    <div class="bar-fill sell-fill" id="sellBar"></div>
                                    <span id="sellPercentage">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="analysis-card price-prediction">
                        <div class="analysis-header">
                            <i class="fas fa-crystal-ball"></i>
                            <h3 id="predictionTitle">Fiyat Tahmini</h3>
                        </div>
                        <div class="prediction-display" id="pricePrediction">
                            <div class="prediction-direction">
                                <i class="fas fa-arrow-up"></i>
                                <span>Hesaplanıyor...</span>
                            </div>
                            <div class="prediction-confidence">Güven: 0%</div>
                        </div>
                    </div>

                    <div class="analysis-card top-activity">
                        <div class="analysis-header">
                            <i class="fas fa-fire"></i>
                            <h3>En Aktif Coin</h3>
                        </div>
                        <div class="activity-display" id="topActivity">
                            <div class="coin-name">-</div>
                            <div class="activity-count">0 işlem</div>
                            <div class="activity-trend">-</div>
                        </div>
                    </div>

                    <div class="analysis-card whale-strength">
                        <div class="analysis-header">
                            <i class="fas fa-dumbbell"></i>
                            <h3>Balina Gücü</h3>
                        </div>
                        <div class="strength-display" id="whaleStrength">
                            <div class="strength-meter">
                                <div class="strength-fill" id="strengthFill"></div>
                            </div>
                            <div class="strength-value" id="strengthValue">Orta</div>
                            <div class="strength-description" id="strengthDescription">Balina aktivitesi normal seviyede</div>
                        </div>
                    </div>

                    <div class="analysis-card market-momentum">
                        <div class="analysis-header">
                            <i class="fas fa-rocket"></i>
                            <h3>Piyasa Momentumu</h3>
                        </div>
                        <div class="momentum-display" id="marketMomentum">
                            <div class="momentum-arrow" id="momentumArrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                            <div class="momentum-value" id="momentumValue">Nötr</div>
                            <div class="momentum-score" id="momentumScore">50/100</div>
                        </div>
                    </div>

                    <div class="analysis-card volume-analysis">
                        <div class="analysis-header">
                            <i class="fas fa-chart-bar"></i>
                            <h3>Hacim Analizi</h3>
                        </div>
                        <div class="volume-display" id="volumeAnalysis">
                            <div class="volume-comparison">
                                <div class="volume-item">
                                    <span>Ortalama:</span>
                                    <span id="avgVolume">$0</span>
                                </div>
                                <div class="volume-item">
                                    <span>Güncel:</span>
                                    <span id="currentVolume">$0</span>
                                </div>
                                <div class="volume-item">
                                    <span>Değişim:</span>
                                    <span id="volumeChange">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="analysis-card fear-greed">
                        <div class="analysis-header">
                            <i class="fas fa-thermometer-half"></i>
                            <h3>Korku & Açgözlülük</h3>
                        </div>
                        <div class="fear-greed-display" id="fearGreedIndex">
                            <div class="fear-greed-meter">
                                <div class="fear-greed-needle" id="fearGreedNeedle"></div>
                                <div class="fear-greed-scale">
                                    <span>Korku</span>
                                    <span>Nötr</span>
                                    <span>Açgözlülük</span>
                                </div>
                            </div>
                            <div class="fear-greed-value" id="fearGreedValue">50</div>
                        </div>
                    </div>

                    <div class="analysis-card reliability-score">
                        <div class="analysis-header">
                            <i class="fas fa-shield-check"></i>
                            <h3>Güvenilirlik Skoru</h3>
                        </div>
                        <div class="reliability-display" id="reliabilityScore">
                            <div class="reliability-meter">
                                <div class="reliability-fill" id="reliabilityFill"></div>
                            </div>
                            <div class="reliability-value" id="reliabilityValue">0%</div>
                            <div class="reliability-description" id="reliabilityDescription">Hesaplanıyor...</div>
                        </div>
                    </div>

                    <div class="analysis-card trend-confidence">
                        <div class="analysis-header">
                            <i class="fas fa-chart-line-up"></i>
                            <h3>Trend Güveni</h3>
                        </div>
                        <div class="trend-display" id="trendConfidence">
                            <div class="trend-strength">
                                <div class="trend-bar" id="trendBar"></div>
                            </div>
                            <div class="trend-value" id="trendValue">Belirsiz</div>
                            <div class="trend-recommendation" id="trendRecommendation">Daha fazla veri bekleniyor</div>
                        </div>
                    </div>
                </div>

                <div class="detailed-analysis" id="detailedAnalysis">
                    <h3 id="detailedAnalysisTitle">📊 Detaylı Analiz</h3>
                    <div class="analysis-text" id="analysisText">
                        Balina hareketleri analiz ediliyor...
                    </div>
                </div>
            </section>

            <!-- Portföy Bölümü -->
            <section class="portfolio-section" id="portfolioSection" style="display: none;">
                <div class="section-header">
                    <h2>💼 Kişisel Portföy</h2>
                    <button class="add-coin-btn" onclick="showAddCoinModal()">
                        <i class="fas fa-plus"></i> Coin Ekle
                    </button>
                </div>

                <div class="portfolio-summary">
                    <div class="portfolio-card">
                        <h3>Toplam Değer</h3>
                        <p class="portfolio-value" id="totalPortfolioValue">$0</p>
                        <span class="portfolio-change" id="totalPortfolioChange">+0%</span>
                    </div>
                    <div class="portfolio-card">
                        <h3>Günlük P&L</h3>
                        <p class="portfolio-pnl" id="dailyPnL">$0</p>
                        <span class="pnl-percentage" id="dailyPnLPercentage">+0%</span>
                    </div>
                    <div class="portfolio-card">
                        <h3>En İyi Performans</h3>
                        <p class="best-performer" id="bestPerformer">-</p>
                        <span class="performer-change" id="bestPerformerChange">+0%</span>
                    </div>
                </div>

                <div class="portfolio-table-container">
                    <table class="portfolio-table">
                        <thead>
                            <tr>
                                <th>Coin</th>
                                <th>Miktar</th>
                                <th>Ortalama Fiyat</th>
                                <th>Güncel Fiyat</th>
                                <th>Değer</th>
                                <th>P&L</th>
                                <th>%</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody id="portfolioTableBody">
                            <!-- JavaScript ile doldurulacak -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Teknik Analiz Bölümü -->
            <section class="technical-analysis-section" id="technicalSection" style="display: none;">
                <h2>📈 Teknik Analiz</h2>
                <div class="technical-grid">
                    <div class="technical-card">
                        <h3>RSI (14)</h3>
                        <div class="rsi-indicator" id="rsiIndicator">
                            <div class="rsi-bar">
                                <div class="rsi-fill" id="rsiFill"></div>
                            </div>
                            <span class="rsi-value" id="rsiValue">50</span>
                        </div>
                    </div>
                    <div class="technical-card">
                        <h3>MACD</h3>
                        <div class="macd-display" id="macdDisplay">
                            <div class="macd-line">MACD: <span id="macdValue">0</span></div>
                            <div class="signal-line">Signal: <span id="signalValue">0</span></div>
                            <div class="histogram">Histogram: <span id="histogramValue">0</span></div>
                        </div>
                    </div>
                    <div class="technical-card">
                        <h3>Bollinger Bands</h3>
                        <div class="bollinger-display" id="bollingerDisplay">
                            <div>Üst: <span id="upperBand">$0</span></div>
                            <div>Orta: <span id="middleBand">$0</span></div>
                            <div>Alt: <span id="lowerBand">$0</span></div>
                        </div>
                    </div>
                    <div class="technical-card">
                        <h3>Support/Resistance</h3>
                        <div class="sr-levels" id="srLevels">
                            <div class="resistance">R1: <span id="resistance1">$0</span></div>
                            <div class="support">S1: <span id="support1">$0</span></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Uyarılar Bölümü -->
            <section class="alerts-section">
                <h2>Son Uyarılar</h2>
                <div class="alerts-container" id="alertsContainer">
                    <!-- JavaScript ile doldurulacak -->
                </div>
            </section>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Kripto Balina Takip Sistemi. Tüm hakları saklıdır.</p>
            <p><strong>🌐 Gerçek Zamanlı Veriler:</strong> Fiyatlar CoinGecko API'den alınmaktadır. Balina işlemleri gerçek fiyatlarla simüle edilmektedir.</p>
            <p><small>⚠️ Yatırım tavsiyesi değildir. Sadece eğitim ve analiz amaçlıdır.</small></p>
        </div>
    </footer>

    <!-- Coin Ekleme Modal -->
    <div class="modal" id="addCoinModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Portföye Coin Ekle</h3>
                <span class="close" onclick="closeAddCoinModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addCoinForm">
                    <div class="form-group">
                        <label for="modalCoinSelect">Coin Seç:</label>
                        <select id="modalCoinSelect" required>
                            <option value="">Coin seçin...</option>
                            <optgroup label="🏆 Top Kripto Paralar">
                                <option value="BTC">Bitcoin (BTC)</option>
                                <option value="ETH">Ethereum (ETH)</option>
                                <option value="BNB">BNB (BNB)</option>
                                <option value="SOL">Solana (SOL)</option>
                                <option value="XRP">XRP (XRP)</option>
                                <option value="DOGE">Dogecoin (DOGE)</option>
                                <option value="ADA">Cardano (ADA)</option>
                                <option value="AVAX">Avalanche (AVAX)</option>
                            </optgroup>
                            <optgroup label="🚀 DeFi Tokens">
                                <option value="UNI">Uniswap (UNI)</option>
                                <option value="LINK">Chainlink (LINK)</option>
                                <option value="AAVE">Aave (AAVE)</option>
                                <option value="MATIC">Polygon (MATIC)</option>
                                <option value="MKR">Maker (MKR)</option>
                                <option value="COMP">Compound (COMP)</option>
                                <option value="SUSHI">SushiSwap (SUSHI)</option>
                                <option value="CRV">Curve DAO (CRV)</option>
                            </optgroup>
                            <optgroup label="🎮 Gaming & NFT">
                                <option value="SAND">The Sandbox (SAND)</option>
                                <option value="MANA">Decentraland (MANA)</option>
                                <option value="AXS">Axie Infinity (AXS)</option>
                                <option value="GALA">Gala (GALA)</option>
                                <option value="ENJ">Enjin Coin (ENJ)</option>
                                <option value="ILV">Illuvium (ILV)</option>
                            </optgroup>
                            <optgroup label="🐕 Meme Coinler">
                                <option value="DOGE">Dogecoin (DOGE)</option>
                                <option value="SHIB">Shiba Inu (SHIB)</option>
                                <option value="PEPE">Pepe (PEPE)</option>
                                <option value="FLOKI">Floki (FLOKI)</option>
                            </optgroup>
                            <optgroup label="🏛️ Klasik Altcoinler">
                                <option value="LTC">Litecoin (LTC)</option>
                                <option value="ETC">Ethereum Classic (ETC)</option>
                                <option value="BCH">Bitcoin Cash (BCH)</option>
                                <option value="XMR">Monero (XMR)</option>
                                <option value="ZEC">Zcash (ZEC)</option>
                                <option value="DASH">Dash (DASH)</option>
                            </optgroup>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="coinAmount">Miktar:</label>
                        <input type="number" id="coinAmount" step="0.00000001" required>
                    </div>
                    <div class="form-group">
                        <label for="buyPrice">Alış Fiyatı ($):</label>
                        <input type="number" id="buyPrice" step="0.01" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="closeAddCoinModal()">İptal</button>
                        <button type="submit">Ekle</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Mod Yardım Modal -->
    <div class="modal" id="modeHelpModal">
        <div class="modal-content mode-help-content">
            <div class="modal-header">
                <h3>🎯 Analiz Modları Rehberi</h3>
                <span class="close" onclick="closeModeHelpModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="mode-help-grid">
                    <div class="mode-help-item">
                        <div class="mode-icon">⚡</div>
                        <h4>Hızlı Modu</h4>
                        <p>Anlık sonuçlar ve basit göstergeler. Hızlı karar almak isteyenler için ideal.</p>
                        <ul>
                            <li>Temel alış/satış sinyalleri</li>
                            <li>Basit trend analizi</li>
                            <li>Hızlı hesaplama</li>
                        </ul>
                    </div>
                    <div class="mode-help-item">
                        <div class="mode-icon">📊</div>
                        <h4>Standart Modu</h4>
                        <p>Dengeli analiz seviyesi. Çoğu kullanıcı için uygun.</p>
                        <ul>
                            <li>Orta seviye analiz</li>
                            <li>Temel risk değerlendirmesi</li>
                            <li>Standart göstergeler</li>
                        </ul>
                    </div>
                    <div class="mode-help-item">
                        <div class="mode-icon">🔮</div>
                        <h4>Gelişmiş Modu</h4>
                        <p>Detaylı analiz ve tahminler. Deneyimli kullanıcılar için.</p>
                        <ul>
                            <li>Gelişmiş sentiment analizi</li>
                            <li>Risk ve volatilite ölçümü</li>
                            <li>Çoklu faktör analizi</li>
                        </ul>
                    </div>
                    <div class="mode-help-item">
                        <div class="mode-icon">💎</div>
                        <h4>Profesyonel Modu</h4>
                        <p>En kapsamlı analiz. Profesyonel yatırımcılar için.</p>
                        <ul>
                            <li>Portföy önerileri</li>
                            <li>Stop-loss seviyeleri</li>
                            <li>Gelişmiş risk yönetimi</li>
                        </ul>
                    </div>
                    <div class="mode-help-item">
                        <div class="mode-icon">🤖</div>
                        <h4>AI Modu</h4>
                        <p>Yapay zeka destekli tahminler ve pattern recognition.</p>
                        <ul>
                            <li>Makine öğrenmesi tahminleri</li>
                            <li>Pattern tanıma</li>
                            <li>Gelecek fiyat projeksiyonları</li>
                        </ul>
                    </div>
                    <div class="mode-help-item">
                        <div class="mode-icon">🔬</div>
                        <h4>Araştırma Modu</h4>
                        <p>Akademik seviyede detaylı analiz ve araştırma verileri.</p>
                        <ul>
                            <li>Derinlemesine istatistikler</li>
                            <li>Akademik göstergeler</li>
                            <li>Araştırma raporları</li>
                        </ul>
                    </div>
                    <div class="mode-help-item">
                        <div class="mode-icon">📈</div>
                        <h4>Teknik Modu</h4>
                        <p>Sadece teknik analiz odaklı. Teknik analiz uzmanları için.</p>
                        <ul>
                            <li>RSI, MACD, Bollinger</li>
                            <li>Support/Resistance</li>
                            <li>Teknik göstergeler</li>
                        </ul>
                    </div>
                    <div class="mode-help-item">
                        <div class="mode-icon">🎯</div>
                        <h4>Otomatik Modu</h4>
                        <p>Piyasa durumuna göre otomatik mod seçimi.</p>
                        <ul>
                            <li>Akıllı mod seçimi</li>
                            <li>Piyasa adaptasyonu</li>
                            <li>Otomatik optimizasyon</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mod Karşılaştırma Modal -->
    <div class="modal" id="modeComparisonModal">
        <div class="modal-content mode-comparison-content">
            <div class="modal-header">
                <h3>⚖️ Mod Karşılaştırması</h3>
                <span class="close" onclick="closeModeComparisonModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="comparison-controls">
                    <select id="compareMode1">
                        <option value="quick">⚡ Hızlı Modu</option>
                        <option value="standard">📊 Standart</option>
                        <option value="advanced" selected>🔮 Gelişmiş</option>
                        <option value="professional">💎 Profesyonel</option>
                        <option value="ai">🤖 AI Modu</option>
                        <option value="research">🔬 Araştırma</option>
                        <option value="technical">📈 Teknik</option>
                    </select>
                    <span class="vs-text">VS</span>
                    <select id="compareMode2">
                        <option value="quick">⚡ Hızlı Modu</option>
                        <option value="standard">📊 Standart</option>
                        <option value="advanced">🔮 Gelişmiş</option>
                        <option value="professional" selected>💎 Profesyonel</option>
                        <option value="ai">🤖 AI Modu</option>
                        <option value="research">🔬 Araştırma</option>
                        <option value="technical">📈 Teknik</option>
                    </select>
                    <button onclick="runModeComparison()">Karşılaştır</button>
                </div>
                <div class="comparison-results" id="comparisonResults">
                    <p>Karşılaştırma için yukarıdaki butona tıklayın.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Mod Geçmişi Modal -->
    <div class="modal" id="modeHistoryModal">
        <div class="modal-content mode-history-content">
            <div class="modal-header">
                <h3>📊 Mod Geçmişi & Performans</h3>
                <span class="close" onclick="closeModeHistoryModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="history-stats">
                    <div class="stat-item">
                        <span>En Çok Kullanılan Mod:</span>
                        <span id="mostUsedMode">🔮 Gelişmiş</span>
                    </div>
                    <div class="stat-item">
                        <span>Toplam Analiz:</span>
                        <span id="totalAnalyses">0</span>
                    </div>
                    <div class="stat-item">
                        <span>En Başarılı Mod:</span>
                        <span id="bestPerformingMode">-</span>
                    </div>
                </div>
                <div class="history-timeline" id="modeHistoryTimeline">
                    <!-- JavaScript ile doldurulacak -->
                </div>
            </div>
        </div>
    </div>

    <!-- Raporlama Bölümü -->
    <div class="reports-section" id="reportsSection" style="display: none;">
        <h2>📊 Raporlar</h2>
        <div class="reports-grid">
            <div class="report-card">
                <h3>Günlük Rapor</h3>
                <p>Son 24 saatlik balina aktivitesi</p>
                <button onclick="generateDailyReport()">PDF İndir</button>
            </div>
            <div class="report-card">
                <h3>Haftalık Rapor</h3>
                <p>Son 7 günlük trend analizi</p>
                <button onclick="generateWeeklyReport()">PDF İndir</button>
            </div>
            <div class="report-card">
                <h3>Portföy Raporu</h3>
                <p>Kişisel portföy performansı</p>
                <button onclick="generatePortfolioReport()">PDF İndir</button>
            </div>
        </div>
    </div>

    <!-- PWA Install Banner -->
    <div class="install-banner" id="installBanner" style="display: none;">
        <div class="install-content">
            <i class="fas fa-mobile-alt"></i>
            <div>
                <h4>Uygulamayı Yükle</h4>
                <p>Daha iyi deneyim için telefona yükleyin</p>
            </div>
            <button onclick="installPWA()">Yükle</button>
            <button onclick="dismissInstallBanner()">&times;</button>
        </div>
    </div>

    <!-- Klavye Kısayolları -->
    <div class="keyboard-shortcuts" id="keyboardShortcuts">
        <h4>⌨️ Klavye Kısayolları</h4>
        <ul>
            <li><kbd>1-8</kbd> Analiz modu değiştir</li>
            <li><kbd>H</kbd> Mod yardımı</li>
            <li><kbd>C</kbd> Mod karşılaştırması</li>
            <li><kbd>G</kbd> Mod geçmişi</li>
            <li><kbd>R</kbd> Analizi yenile</li>
            <li><kbd>?</kbd> Bu yardımı göster/gizle</li>
        </ul>
    </div>

    <script>
        // Dinamik versiyonlama ile cache-busting
        const version = Date.now();
        const script = document.createElement('script');
        script.src = `script.js?v=${version}`;
        document.head.appendChild(script);

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = `styles.css?v=${version}`;
        document.head.appendChild(link);
    </script>

    <!-- PWA Registration -->
    <script>
        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            document.getElementById('installBanner').style.display = 'block';
        });

        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                    document.getElementById('installBanner').style.display = 'none';
                });
            }
        }

        function dismissInstallBanner() {
            document.getElementById('installBanner').style.display = 'none';
        }
    </script>
</body>
</html>
